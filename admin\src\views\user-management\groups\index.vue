<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->

<script setup lang="ts">
import { computed, h, reactive, ref } from 'vue';
import type { DataTableRowKey } from 'naive-ui';
import { NButton, NCard, NDataTable, NGrid, NGi, NInput, NPagination, NPopconfirm, NSelect, NSpace, NTag, useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';

defineOptions({
  name: 'UserGroups'
});

const message = useMessage();
const { t } = useI18n();

// 数据类型定义
interface GroupInfo {
  id: string;
  name: string;
  description: string;
  color: string;
  permissions: string[];
  userCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 响应式数据
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref<string | null>(null);
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 模拟数据
const groups = ref<GroupInfo[]>([
  {
    id: '1',
    name: '管理员',
    description: '系统管理员，拥有所有权限',
    color: '#ff6b6b',
    permissions: ['user:read', 'user:write', 'order:read', 'order:write'],
    userCount: 5,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  },
  {
    id: '2',
    name: 'VIP用户',
    description: 'VIP会员，享受特殊权限',
    color: '#4ecdc4',
    permissions: ['user:read', 'order:read'],
    userCount: 128,
    status: 'active',
    createdAt: '2024-01-02',
    updatedAt: '2024-01-16'
  },
  {
    id: '3',
    name: '普通用户',
    description: '普通用户分组',
    color: '#45b7d1',
    permissions: ['user:read'],
    userCount: 1024,
    status: 'active',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-17'
  },
  {
    id: '4',
    name: '测试分组',
    description: '用于测试的分组',
    color: '#96ceb4',
    permissions: ['user:read'],
    userCount: 0,
    status: 'inactive',
    createdAt: '2024-01-04',
    updatedAt: '2024-01-18'
  }
]);

// 状态选项
const statusOptions = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'inactive' }
];

// 权限选项
const permissionOptions = [
  { label: '用户查看', value: 'user:read' },
  { label: '用户编辑', value: 'user:write' },
  { label: '订单查看', value: 'order:read' },
  { label: '订单编辑', value: 'order:write' },
  { label: '商品查看', value: 'product:read' },
  { label: '商品编辑', value: 'product:write' }
];

// 筛选后的数据
const filteredGroups = computed(() => {
  return groups.value.filter(group => {
    const matchesSearch = !searchQuery.value || 
      group.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      group.description.toLowerCase().includes(searchQuery.value.toLowerCase());
    const matchesStatus = !selectedStatus.value || group.status === selectedStatus.value;
    return matchesSearch && matchesStatus;
  });
});

// 分页后的数据
const paginatedGroups = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  pagination.total = filteredGroups.value.length;
  return filteredGroups.value.slice(start, end);
});

// 表格列定义
const columns = computed(() => [
  {
    type: 'selection'
  },
  {
    title: '分组名称',
    key: 'name',
    width: 180,
    render: (row: GroupInfo) => h('div', { class: 'flex items-center gap-8px' }, [
      h('div', {
        class: 'w-12px h-12px rounded-full flex-shrink-0',
        style: { backgroundColor: row.color }
      }),
      h('span', { class: 'font-medium' }, row.name)
    ])
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '权限',
    key: 'permissions',
    width: 200,
    render: (row: GroupInfo) => {
      const visibleTags = row.permissions.slice(0, 2).map((permission: string) => {
        const option = permissionOptions.find(opt => opt.value === permission);
        return h(NTag, { size: 'small', type: 'info', bordered: false }, 
          { default: () => option?.label || permission });
      });

      if (row.permissions.length > 2) {
        visibleTags.push(
          h(NTag, { size: 'small', type: 'default', bordered: false }, 
            { default: () => `+${row.permissions.length - 2}` })
        );
      }

      return h('div', { class: 'flex flex-wrap gap-4px' }, visibleTags);
    }
  },
  {
    title: '用户数量',
    key: 'userCount',
    width: 100,
    align: 'center',
    render: (row: GroupInfo) => h('span', { class: 'font-medium text-primary' }, row.userCount)
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render: (row: GroupInfo) => {
      const config = row.status === 'active'
        ? { type: 'success' as const, label: '启用' }
        : { type: 'error' as const, label: '禁用' };
      return h(NTag, { type: config.type, size: 'small', bordered: false },
        { default: () => config.label });
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 240,
    fixed: 'right',
    render: (row: GroupInfo) => {
      return h('div', { class: 'flex items-center gap-8px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleViewMembers(row)
        }, { default: () => '查看成员' }),
        h(NButton, {
          size: 'small',
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h(NButton, {
          size: 'small',
          type: row.status === 'active' ? 'warning' : 'success',
          onClick: () => handleToggleStatus(row.id)
        }, { default: () => row.status === 'active' ? '禁用' : '启用' }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDelete(row.id)
        }, {
          default: () => '确认删除这个分组？',
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            disabled: row.userCount > 0
          }, { default: () => '删除' })
        })
      ]);
    }
  }
]);

// 事件处理函数
function handleSearch() {
  pagination.page = 1;
}

function handleReset() {
  searchQuery.value = '';
  selectedStatus.value = null;
  pagination.page = 1;
}

function handleAdd() {
  message.info('添加分组功能开发中...');
}

function handleViewMembers(row: GroupInfo) {
  message.info(`查看分组 "${row.name}" 的成员`);
}

function handleEdit(row: GroupInfo) {
  message.info(`编辑分组 "${row.name}"`);
}

function handleToggleStatus(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group) {
    group.status = group.status === 'active' ? 'inactive' : 'active';
    message.success(`分组状态已${group.status === 'active' ? '启用' : '禁用'}`);
  }
}

function handleDelete(id: string) {
  const index = groups.value.findIndex(g => g.id === id);
  if (index > -1) {
    groups.value.splice(index, 1);
    message.success('分组删除成功');
  }
}

function handleBatchEnable() {
  message.info(`批量启用 ${checkedRowKeys.value.length} 个分组`);
}

function handleBatchDisable() {
  message.info(`批量禁用 ${checkedRowKeys.value.length} 个分组`);
}

function handleBatchDelete() {
  message.info(`批量删除 ${checkedRowKeys.value.length} 个分组`);
}

function updatePagination() {
  // 分页更新逻辑
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NSpace vertical :size="16">
      <!-- 页面头部 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-20px font-bold text-text">
            用户分组管理
          </h1>
          <p class="text-14px text-text-3 mt-4px">
            管理用户分组和权限配置
          </p>
        </div>
        <div class="flex items-center gap-12px">
          <NButton type="primary" @click="handleAdd">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            创建分组
          </NButton>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <NCard :bordered="false" class="card-wrapper">
        <NSpace vertical :size="16">
          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NGi span="24 s:24 m:8">
              <NInput
                v-model="searchQuery"
                placeholder="请输入分组名称或描述搜索"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <SvgIcon icon="mdi:magnify" />
                </template>
              </NInput>
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedStatus"
                placeholder="选择状态"
                :options="statusOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:8">
              <NSpace>
                <NButton type="primary" @click="handleSearch">
                  <template #icon>
                    <SvgIcon icon="mdi:magnify" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="handleReset">
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" />
                  </template>
                  重置
                </NButton>
              </NSpace>
            </NGi>
          </NGrid>

          <!-- 批量操作 -->
          <div class="flex items-center justify-between pt-16px border-t border-gray-200 dark:border-gray-700">
            <div class="text-14px text-gray-600 dark:text-gray-400">
              已选择 {{ checkedRowKeys.length }} 个分组
            </div>
            <NSpace>
              <NButton
                size="small"
                type="success"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchEnable"
              >
                批量启用
              </NButton>
              <NButton
                size="small"
                type="warning"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDisable"
              >
                批量禁用
              </NButton>
              <NButton
                size="small"
                type="error"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDelete"
              >
                批量删除
              </NButton>
            </NSpace>
          </div>
        </NSpace>
      </NCard>

      <!-- 分组列表表格 -->
      <NCard :bordered="false" class="card-wrapper">
        <NDataTable
          v-model:checkedRowKeys="checkedRowKeys"
          :columns="columns"
          :data="paginatedGroups"
          :row-key="(row: GroupInfo) => row.id"
          :scroll-x="1200"
          flex-height
          style="min-height: 400px"
        />

        <div class="flex items-center justify-between mt-16px">
          <div class="text-14px text-gray-600">
            显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} - {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，共 {{ pagination.total }} 条记录
          </div>
          <NPagination
            v-model:page="pagination.page"
            v-model:pageSize="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            show-size-picker
            show-quick-jumper
            @update:page="updatePagination"
            @update:pageSize="updatePagination"
          />
        </div>
      </NCard>
    </NSpace>
  </div>
</template>

<style scoped>
/* 必要的组件深度样式，UnoCSS无法完全替代的部分 */
</style>
