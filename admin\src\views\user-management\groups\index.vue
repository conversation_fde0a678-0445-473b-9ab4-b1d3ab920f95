<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref, computed, h, reactive } from 'vue';
import type { DataTableRowKey } from 'naive-ui';
import { NButton, NTag, NPopconfirm, NPagination, useMessage, useDialog } from 'naive-ui';
import { useI18n } from 'vue-i18n';

defineOptions({
  name: 'UserGroups'
});

const message = useMessage();
const dialog = useDialog();
const { t } = useI18n();

interface GroupInfo {
  id: string;
  name: string;
  description: string;
  color: string;
  userCount: number;
  permissions: string[];
  status: 'active' | 'disabled';
  createTime: string;
  updateTime: string;
}

// 模拟分组数据
const groups = ref<GroupInfo[]>([
  {
    id: '1',
    name: '普通用户',
    description: '系统默认用户分组，拥有基础权限',
    color: '#3b82f6',
    userCount: 8456,
    permissions: ['browse', 'purchase', 'comment'],
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  },
  {
    id: '2',
    name: 'VIP用户',
    description: 'VIP会员用户，享受专属权益和服务',
    color: '#f59e0b',
    userCount: 2345,
    permissions: ['browse', 'purchase', 'comment', 'vip_discount', 'priority_support'],
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-12-15 10:30:00'
  },
  {
    id: '3',
    name: '超级VIP',
    description: '超级VIP用户，享受最高级别的权益',
    color: '#ef4444',
    userCount: 567,
    permissions: ['browse', 'purchase', 'comment', 'vip_discount', 'priority_support', 'exclusive_products'],
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-12-20 14:20:00'
  },
  {
    id: '4',
    name: '商家用户',
    description: '入驻商家用户，可以发布和管理商品',
    color: '#10b981',
    userCount: 234,
    permissions: ['browse', 'purchase', 'comment', 'merchant_manage', 'product_publish'],
    status: 'active',
    createTime: '2024-02-01 00:00:00',
    updateTime: '2024-12-10 09:15:00'
  },
  {
    id: '5',
    name: '测试用户',
    description: '用于系统测试的用户分组',
    color: '#6b7280',
    userCount: 12,
    permissions: ['browse'],
    status: 'disabled',
    createTime: '2024-03-01 00:00:00',
    updateTime: '2024-03-01 00:00:00'
  }
]);

// 搜索
const searchQuery = ref('');
const selectedStatus = ref<string | null>(null);

// 选中的行
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 加载状态
const loading = ref(false);

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 显示创建/编辑分组模态框
const showGroupModal = ref(false);
const editingGroup = ref<GroupInfo | null>(null);

// 显示分组成员管理抽屉
const showMembersDrawer = ref(false);
const currentGroupForMembers = ref<GroupInfo | null>(null);

// 分组表单数据
const groupForm = ref({
  name: '',
  description: '',
  color: '#3b82f6',
  permissions: [] as string[]
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 2, max: 20, message: '分组名称长度应为2-20个字符', trigger: 'blur' }
  ]
};

// 表单引用
const formRef = ref();

// 权限选项
const permissionOptions = [
  { label: '浏览权限', value: 'browse' },
  { label: '购买权限', value: 'purchase' },
  { label: '评论权限', value: 'comment' },
  { label: 'VIP折扣', value: 'vip_discount' },
  { label: '优先客服', value: 'priority_support' },
  { label: '专属商品', value: 'exclusive_products' },
  { label: '商家管理', value: 'merchant_manage' },
  { label: '商品发布', value: 'product_publish' }
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'disabled' }
];

// 过滤后的分组数据
const filteredGroups = computed(() => {
  let result = groups.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(group => 
      group.name.toLowerCase().includes(query) ||
      group.description.toLowerCase().includes(query)
    );
  }
  
  if (selectedStatus.value) {
    result = result.filter(group => group.status === selectedStatus.value);
  }
  
  return result;
});

// 分页后的分组
const paginatedGroups = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  pagination.total = filteredGroups.value.length;
  return filteredGroups.value.slice(start, end);
});

// 表格列定义
const columns = computed(() => [
  {
    type: 'selection'
  },
  {
    title: t('page.userManagement.groups.table.name'),
    key: 'name',
    width: 180,
    minWidth: 150,
    render: (row: any) => h('div', { class: 'flex items-center gap-8px' }, [
      h('div', {
        class: 'w-12px h-12px rounded-full flex-shrink-0',
        style: { backgroundColor: row.color }
      }),
      h('span', {
        class: 'font-medium text-gray-900 dark:text-gray-100',
        title: row.name
      }, row.name)
    ])
  },
  {
    title: t('page.userManagement.groups.table.description'),
    key: 'description',
    width: 200,
    minWidth: 150,
    ellipsis: {
      tooltip: true
    },
    render: (row: any) => h('span', {
      class: 'text-gray-600',
      title: row.description
    }, row.description || '-')
  },
  {
    title: t('page.userManagement.groups.table.userCount'),
    key: 'userCount',
    width: 120,
    align: 'center',
    render: (row: any) => h('span', {
      class: 'font-medium text-primary'
    }, row.userCount.toLocaleString())
  },
  {
    title: t('page.userManagement.groups.table.permissions'),
    key: 'permissions',
    width: 220,
    minWidth: 180,
    render: (row: any) => {
      if (row.permissions.length === 0) {
        return h('span', { class: 'text-gray-400' }, t('page.userManagement.groups.noPermissions'));
      }

      const visibleTags = row.permissions.slice(0, 2).map((permission: string) => {
        const option = permissionOptions.find((opt: any) => opt.value === permission);
        return h(NTag, {
          size: 'small',
          type: 'info',
          bordered: false
        }, { default: () => option?.label || permission });
      });

      if (row.permissions.length > 2) {
        visibleTags.push(
          h(NTag, {
            size: 'small',
            type: 'default',
            bordered: false
          }, { default: () => `+${row.permissions.length - 2}` })
        );
      }

      return h('div', {
        class: 'flex flex-wrap gap-4px',
        title: row.permissions.map((p: string) => {
          const opt = permissionOptions.find((o: any) => o.value === p);
          return opt?.label || p;
        }).join(', ')
      }, visibleTags);
    }
  },
  {
    title: t('page.userManagement.groups.table.status'),
    key: 'status',
    width: 100,
    align: 'center',
    render: (row: any) => {
      const statusMap = {
        active: { type: 'success' as const, label: t('page.userManagement.groups.status.active') },
        disabled: { type: 'error' as const, label: t('page.userManagement.groups.status.disabled') }
      };
      const config = statusMap[row.status];
      return h(NTag, {
        type: config.type,
        size: 'small',
        bordered: false
      }, { default: () => config.label });
    }
  },
  {
    title: t('page.userManagement.groups.table.updateTime'),
    key: 'updateTime',
    width: 140,
    render: (row: any) => h('span', {
      class: 'text-gray-600',
      title: row.updateTime
    }, row.updateTime.split(' ')[0])
  },
  {
    title: t('page.userManagement.groups.table.actions'),
    key: 'actions',
    width: 280,
    minWidth: 240,
    fixed: 'right',
    render: (row: any) => {
      return h('div', { class: 'flex items-center gap-8px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleViewMembers(row),
          title: t('page.userManagement.groups.viewMembers')
        }, {
          icon: () => h('i', { class: 'i-mdi-account-group' }),
          default: () => '查看成员'
        }),
        h(NButton, {
          size: 'small',
          onClick: () => handleEdit(row),
          title: t('page.userManagement.groups.edit')
        }, {
          icon: () => h('i', { class: 'i-mdi-pencil' }),
          default: () => '编辑'
        }),
        h(NButton, {
          size: 'small',
          type: row.status === 'active' ? 'warning' : 'success',
          onClick: () => handleToggleStatus(row.id),
          title: row.status === 'active' ? t('page.userManagement.groups.disable') : t('page.userManagement.groups.enable')
        }, {
          icon: () => h('i', {
            class: row.status === 'active' ? 'i-mdi-pause' : 'i-mdi-play'
          }),
          default: () => row.status === 'active' ? '禁用' : '启用'
        }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDelete(row.id)
        }, {
          default: () => t('page.userManagement.groups.confirmDelete'),
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            disabled: row.userCount > 0,
            title: row.userCount > 0 ? t('page.userManagement.groups.cannotDeleteWithUsers') : t('page.userManagement.groups.delete')
          }, {
            icon: () => h('i', { class: 'i-mdi-delete' }),
            default: () => '删除'
          })
        })
      ]);
    }
  }
]);

// 操作函数
function handleSearch() {
  // 搜索逻辑已在 computed 中处理
}

function handleReset() {
  searchQuery.value = '';
  selectedStatus.value = null;
  pagination.page = 1;
}

// 重置所有筛选
function handleResetAllFilters() {
  handleReset();
}

// 导出数据
function handleExportGroups() {
  message.success('导出功能开发中...');
}

// 更新分页
function updatePagination() {
  // 分页更新时的逻辑
}

function handleAdd() {
  editingGroup.value = null;
  groupForm.value = {
    name: '',
    description: '',
    color: '#3b82f6',
    permissions: []
  };
  showGroupModal.value = true;
}

function handleEdit(group: GroupInfo) {
  editingGroup.value = group;
  groupForm.value = {
    name: group.name,
    description: group.description,
    color: group.color,
    permissions: [...group.permissions]
  };
  showGroupModal.value = true;
}

function handleViewMembers(group: GroupInfo) {
  currentGroupForMembers.value = group;
  showMembersDrawer.value = true;
}

function handleToggleStatus(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group) {
    group.status = group.status === 'active' ? 'disabled' : 'active';
    group.updateTime = new Date().toLocaleString();
    message.success(group.status === 'active' ? t('page.userManagement.groups.enableSuccess') : t('page.userManagement.groups.disableSuccess'));
  }
}

function handleClone(group: GroupInfo) {
  const clonedGroup: GroupInfo = {
    id: Date.now().toString(),
    name: `${group.name} - ${t('page.userManagement.groups.copy')}`,
    description: group.description,
    color: group.color,
    permissions: [...group.permissions],
    userCount: 0,
    status: 'active',
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString()
  };
  groups.value.push(clonedGroup);
  message.success(t('page.userManagement.groups.cloneSuccess'));
}

function handleQuickPermissions() {
  dialog.info({
    title: t('page.userManagement.groups.quickSetPermissions'),
    content: '选择预设权限模板快速配置分组权限',
    positiveText: t('common.confirm'),
    onPositiveClick: () => {
      message.info('快速权限设置功能开发中...');
    }
  });
}

async function handleSave() {
  try {
    await formRef.value?.validate();

    // 检查分组名称是否重复
    const existingGroup = groups.value.find(g =>
      g.name === groupForm.value.name.trim() &&
      g.id !== editingGroup.value?.id
    );

    if (existingGroup) {
      message.error(t('page.userManagement.groups.nameExists'));
      return;
    }

    if (editingGroup.value) {
      // 编辑分组
      const index = groups.value.findIndex(g => g.id === editingGroup.value!.id);
      if (index > -1) {
        groups.value[index] = {
          ...groups.value[index],
          ...groupForm.value,
          updateTime: new Date().toLocaleString()
        };
        message.success(t('page.userManagement.groups.updateSuccess'));
      }
    } else {
      // 新增分组
      const newGroup: GroupInfo = {
        id: Date.now().toString(),
        ...groupForm.value,
        userCount: 0,
        status: 'active',
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      };
      groups.value.push(newGroup);
      message.success(t('page.userManagement.groups.createSuccess'));
    }

    showGroupModal.value = false;
  } catch (error) {
    // 表单验证失败
  }
}

function handleDelete(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group && group.userCount > 0) {
    message.error(t('page.userManagement.groups.cannotDeleteWithUsers'));
    return;
  }

  const index = groups.value.findIndex(group => group.id === id);
  if (index > -1) {
    groups.value.splice(index, 1);
    message.success(t('page.userManagement.groups.deleteSuccess'));
  }
}



function handleBatchEnable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning(t('page.userManagement.groups.selectGroups'));
    return;
  }

  checkedRowKeys.value.forEach(id => {
    const index = groups.value.findIndex(group => group.id === id);
    if (index > -1) {
      groups.value[index].status = 'active';
      groups.value[index].updateTime = new Date().toLocaleString();
    }
  });
  checkedRowKeys.value = [];
  message.success(t('page.userManagement.groups.batchEnableSuccess'));
}

function handleBatchDisable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning(t('page.userManagement.groups.selectGroups'));
    return;
  }

  dialog.warning({
    title: t('common.confirm'),
    content: `确认禁用选中的 ${checkedRowKeys.value.length} 个分组？`,
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: () => {
      checkedRowKeys.value.forEach(id => {
        const index = groups.value.findIndex(group => group.id === id);
        if (index > -1) {
          groups.value[index].status = 'disabled';
          groups.value[index].updateTime = new Date().toLocaleString();
        }
      });
      checkedRowKeys.value = [];
      message.success(t('page.userManagement.groups.batchDisableSuccess'));
    }
  });
}

function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    message.warning(t('page.userManagement.groups.selectGroups'));
    return;
  }

  // 检查是否有分组包含用户
  const groupsWithUsers = checkedRowKeys.value.filter(id => {
    const group = groups.value.find(g => g.id === id);
    return group && group.userCount > 0;
  });

  if (groupsWithUsers.length > 0) {
    message.error(t('page.userManagement.groups.cannotDeleteWithUsers'));
    return;
  }

  dialog.warning({
    title: t('common.confirmDelete'),
    content: t('page.userManagement.groups.confirmBatchDelete', { count: checkedRowKeys.value.length }),
    positiveText: t('common.delete'),
    negativeText: t('common.cancel'),
    onPositiveClick: () => {
      checkedRowKeys.value.forEach(id => {
        const index = groups.value.findIndex(group => group.id === id);
        if (index > -1) {
          groups.value.splice(index, 1);
        }
      });
      checkedRowKeys.value = [];
      message.success(t('page.userManagement.groups.batchDeleteSuccess'));
    }
  });
}

// 排序功能
function handleSort(column: string, order: 'asc' | 'desc') {
  groups.value.sort((a, b) => {
    let aValue = a[column as keyof GroupInfo];
    let bValue = b[column as keyof GroupInfo];

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = (bValue as string).toLowerCase();
    }

    if (order === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
}

// 模拟加载数据
async function loadGroups() {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    // 数据已经在 groups.value 中
  } catch (error) {
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
}

// 初始化加载数据
loadGroups();
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
          {{ $t('page.userManagement.groups.title') }}
        </h1>
        <p class="text-14px text-gray-600 dark:text-gray-400">
          {{ $t('page.userManagement.groups.description') }}
        </p>
      </div>
      <div class="flex items-center space-x-12px">
        <NButton type="primary" @click="handleAdd">
          <template #icon>
            <SvgIcon icon="mdi:plus" />
          </template>
          {{ $t('page.userManagement.groups.createGroup') }}
        </NButton>
        <NButton @click="handleQuickPermissions">
          <template #icon>
            <SvgIcon icon="mdi:shield-check" />
          </template>
          快速权限设置
        </NButton>
        <NButton @click="handleExportGroups">
          <template #icon>
            <SvgIcon icon="mdi:download" />
          </template>
          导出数据
        </NButton>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <NCard :bordered="false" class="card-wrapper">
      <NSpace vertical :size="16">
        <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGi span="24 s:24 m:8">
            <NInput
              v-model="searchQuery"
              :placeholder="$t('page.userManagement.groups.searchPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <SvgIcon icon="mdi:magnify" />
              </template>
            </NInput>
          </NGi>
          <NGi span="24 s:12 m:4">
            <NSelect
              v-model="selectedStatus"
              :placeholder="$t('page.userManagement.groups.selectStatus')"
              :options="statusOptions"
              clearable
            />
          </NGi>
          <NGi span="24 s:12 m:8">
            <NSpace>
              <NButton type="primary" @click="handleSearch">
                <template #icon>
                  <SvgIcon icon="mdi:magnify" />
                </template>
                {{ $t('page.userManagement.groups.search') }}
              </NButton>
              <NButton @click="handleReset">
                <template #icon>
                  <SvgIcon icon="mdi:refresh" />
                </template>
                {{ $t('page.userManagement.groups.reset') }}
              </NButton>
              <NButton @click="handleResetAllFilters">
                <template #icon>
                  <SvgIcon icon="mdi:filter-remove" />
                </template>
                {{ $t('page.userManagement.groups.resetFilter') }}
              </NButton>
            </NSpace>
          </NGi>
        </NGrid>

        <!-- 批量操作 -->
        <div class="flex items-center justify-between pt-16px border-t border-gray-200 dark:border-gray-700">
          <div class="text-14px text-gray-600 dark:text-gray-400">
            已选择 {{ checkedRowKeys.length }} 个分组
          </div>
          <NSpace>
            <NButton
              size="small"
              type="success"
              :disabled="checkedRowKeys.length === 0"
              @click="handleBatchEnable"
            >
              {{ $t('page.userManagement.groups.batchEnable') }}
            </NButton>
            <NButton
              size="small"
              type="warning"
              :disabled="checkedRowKeys.length === 0"
              @click="handleBatchDisable"
            >
              {{ $t('page.userManagement.groups.batchDisable') }}
            </NButton>
            <NButton
              size="small"
              type="error"
              :disabled="checkedRowKeys.length === 0"
              @click="handleBatchDelete"
            >
              {{ $t('page.userManagement.groups.batchDelete') }}
            </NButton>
          </NSpace>
        </div>
      </NSpace>
    </NCard>

    <!-- 分组列表表格 -->
    <NCard :bordered="false" class="card-wrapper">
      <NDataTable
        v-model:checkedRowKeys="checkedRowKeys"
        :columns="columns"
        :data="paginatedGroups"
        :row-key="(row: GroupInfo) => row.id"
        :scroll-x="1400"
        flex-height
        style="min-height: 400px"
        class="responsive-table"
      />

      <div class="flex items-center justify-between mt-16px">
        <div class="text-14px text-gray-600">
          显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} - {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，共 {{ pagination.total }} 条记录
        </div>
        <NPagination
          v-model:page="pagination.page"
          v-model:pageSize="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          show-size-picker
          show-quick-jumper
          @update:page="updatePagination"
          @update:page-size="updatePagination"
        />
      </div>
    </NCard>

    <!-- 创建/编辑分组模态框 -->
    <NModal v-model:show="showGroupModal" preset="dialog" :title="editingGroup ? $t('page.userManagement.groups.modal.editTitle') : $t('page.userManagement.groups.modal.createTitle')" style="width: 500px">
      <NForm ref="formRef" :model="groupForm" :rules="formRules" label-placement="top">
        <NSpace vertical :size="16">
          <NFormItem :label="$t('page.userManagement.groups.form.name')" path="name" required>
            <NInput v-model:value="groupForm.name" :placeholder="$t('page.userManagement.groups.form.namePlaceholder')" />
          </NFormItem>

          <NFormItem :label="$t('page.userManagement.groups.form.description')">
            <NInput
              v-model:value="groupForm.description"
              type="textarea"
              :placeholder="$t('page.userManagement.groups.form.descriptionPlaceholder')"
              :rows="3"
            />
          </NFormItem>

          <NFormItem :label="$t('page.userManagement.groups.form.color')">
            <NColorPicker v-model:value="groupForm.color" />
          </NFormItem>

          <NFormItem :label="$t('page.userManagement.groups.form.permissions')">
            <NCheckboxGroup v-model:value="groupForm.permissions">
              <NGrid :x-gap="12" :y-gap="8" :cols="2">
                <NGi v-for="option in permissionOptions" :key="option.value">
                  <NCheckbox :value="option.value">
                    {{ option.label }}
                  </NCheckbox>
                </NGi>
              </NGrid>
            </NCheckboxGroup>
          </NFormItem>
        </NSpace>
      </NForm>

      <template #action>
        <NSpace>
          <NButton @click="showGroupModal = false">{{ $t('page.userManagement.groups.form.cancel') }}</NButton>
          <NButton type="primary" @click="handleSave">{{ $t('page.userManagement.groups.form.save') }}</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 分组成员管理抽屉 -->
    <NDrawer v-model:show="showMembersDrawer" :width="600" placement="right">
      <NDrawerContent :title="`${currentGroupForMembers?.name} - ${$t('page.userManagement.groups.drawer.membersTitle')}`" closable>
        <div v-if="currentGroupForMembers" class="space-y-16px">
          <!-- 成员统计 -->
          <NCard size="small">
            <NStatistic :label="$t('page.userManagement.groups.drawer.memberCount')" :value="currentGroupForMembers.userCount" />
          </NCard>

          <!-- 成员列表 -->
          <NCard :title="$t('page.userManagement.groups.drawer.memberCount')" size="small">
            <div v-if="currentGroupForMembers.userCount === 0" class="text-center py-40px">
              <SvgIcon icon="mdi:account-group-outline" class="text-60px text-gray-400 mb-16px" />
              <p class="text-gray-500">{{ $t('page.userManagement.groups.drawer.noMembers') }}</p>
            </div>
            <div v-else class="space-y-8px">
              <!-- 这里可以添加成员列表，暂时显示占位内容 -->
              <div class="flex items-center justify-between p-12px border border-gray-200 rounded-6px">
                <div class="flex items-center space-x-12px">
                  <NAvatar size="small" />
                  <span>示例用户</span>
                </div>
                <NButton size="small" type="error" text>{{ $t('page.userManagement.groups.drawer.removeMember') }}</NButton>
              </div>
            </div>
          </NCard>

          <!-- 添加成员 -->
          <NCard :title="$t('page.userManagement.groups.drawer.addMember')" size="small">
            <NSpace vertical :size="12">
              <NSelect
                :placeholder="$t('page.userManagement.groups.drawer.searchUser')"
                filterable
                clearable
                :options="[]"
              />
              <NButton type="primary" size="small">{{ $t('page.userManagement.groups.drawer.addToGroup') }}</NButton>
            </NSpace>
          </NCard>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
/* 必要的组件深度样式，UnoCSS无法完全替代的部分 */



</style>
