<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->

<script setup lang="ts">
import { computed, h, reactive, ref } from 'vue';
import type { DataTableRowKey } from 'naive-ui';
import { NButton, NCard, NDataTable, NGrid, NGi, NInput, NPagination, NPopconfirm, NSelect, NSpace, NTag, useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';

defineOptions({
  name: 'UserGroups'
});

const message = useMessage();
const { t } = useI18n();

// 数据类型定义
interface GroupInfo {
  id: string;
  name: string;
  description: string;
  color: string;
  permissions: string[];
  userCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 响应式数据
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref<string | null>(null);
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 模拟数据
const groups = ref<GroupInfo[]>([
  {
    id: '1',
    name: '管理员',
    description: '系统管理员，拥有所有权限',
    color: '#ff6b6b',
    permissions: ['user:read', 'user:write', 'order:read', 'order:write'],
    userCount: 5,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  },
  {
    id: '2',
    name: 'VIP用户',
    description: 'VIP会员，享受特殊权限',
    color: '#4ecdc4',
    permissions: ['user:read', 'order:read'],
    userCount: 128,
    status: 'active',
    createdAt: '2024-01-02',
    updatedAt: '2024-01-16'
  },
  {
    id: '3',
    name: '普通用户',
    description: '普通用户分组',
    color: '#45b7d1',
    permissions: ['user:read'],
    userCount: 1024,
    status: 'active',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-17'
  },
  {
    id: '4',
    name: '测试分组',
    description: '用于测试的分组',
    color: '#96ceb4',
    permissions: ['user:read'],
    userCount: 0,
    status: 'inactive',
    createdAt: '2024-01-04',
    updatedAt: '2024-01-18'
  }
]);

// 状态选项
const statusOptions = computed(() => [
  { label: t('page.userManagement.groups.status.active'), value: 'active' },
  { label: t('page.userManagement.groups.status.disabled'), value: 'inactive' }
]);

// 权限选项
const permissionOptions = computed(() => [
  { label: t('page.userManagement.groups.permissions.browse'), value: 'user:read' },
  { label: t('page.userManagement.groups.permissions.purchase'), value: 'user:write' },
  { label: t('page.userManagement.groups.permissions.comment'), value: 'order:read' },
  { label: t('page.userManagement.groups.permissions.vipDiscount'), value: 'order:write' },
  { label: t('page.userManagement.groups.permissions.prioritySupport'), value: 'product:read' },
  { label: t('page.userManagement.groups.permissions.exclusiveProducts'), value: 'product:write' }
]);

// 筛选后的数据
const filteredGroups = computed(() => {
  return groups.value.filter(group => {
    const matchesSearch = !searchQuery.value || 
      group.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      group.description.toLowerCase().includes(searchQuery.value.toLowerCase());
    const matchesStatus = !selectedStatus.value || group.status === selectedStatus.value;
    return matchesSearch && matchesStatus;
  });
});

// 分页后的数据
const paginatedGroups = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  pagination.total = filteredGroups.value.length;
  return filteredGroups.value.slice(start, end);
});

// 表格列定义
const columns = computed(() => [
  {
    type: 'selection'
  },
  {
    title: t('page.userManagement.groups.table.name'),
    key: 'name',
    width: 160,
    render: (row: GroupInfo) => h('div', { class: 'flex items-center gap-8px' }, [
      h('div', {
        class: 'w-12px h-12px rounded-full flex-shrink-0',
        style: { backgroundColor: row.color }
      }),
      h('span', { class: 'font-medium' }, row.name)
    ])
  },
  {
    title: t('page.userManagement.groups.table.description'),
    key: 'description',
    width: 220,
    ellipsis: true
  },
  {
    title: t('page.userManagement.groups.table.permissions'),
    key: 'permissions',
    width: 240,
    render: (row: GroupInfo) => {
      const visibleTags = row.permissions.slice(0, 2).map((permission: string) => {
        const option = permissionOptions.value.find(opt => opt.value === permission);
        return h(NTag, { size: 'small', type: 'info', bordered: false },
          { default: () => option?.label || permission });
      });

      if (row.permissions.length > 2) {
        visibleTags.push(
          h(NTag, { size: 'small', type: 'default', bordered: false }, 
            { default: () => `+${row.permissions.length - 2}` })
        );
      }

      return h('div', { class: 'flex flex-wrap gap-4px' }, visibleTags);
    }
  },
  {
    title: t('page.userManagement.groups.table.userCount'),
    key: 'userCount',
    width: 120,
    align: 'center',
    render: (row: GroupInfo) => h('span', { class: 'font-medium text-primary' }, row.userCount)
  },
  {
    title: t('page.userManagement.groups.table.status'),
    key: 'status',
    width: 100,
    align: 'center',
    render: (row: GroupInfo) => {
      const config = row.status === 'active'
        ? { type: 'success' as const, label: t('page.userManagement.groups.status.active') }
        : { type: 'error' as const, label: t('page.userManagement.groups.status.disabled') };
      return h(NTag, { type: config.type, size: 'small', bordered: false },
        { default: () => config.label });
    }
  },
  {
    title: t('common.updateTime'),
    key: 'createdAt',
    width: 160
  },
  {
    title: t('page.userManagement.groups.table.actions'),
    key: 'actions',
    width: 280,
    fixed: 'right',
    render: (row: GroupInfo) => {
      return h('div', { class: 'flex items-center gap-6px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleViewMembers(row)
        }, { default: () => t('page.userManagement.groups.viewMembers') }),
        h(NButton, {
          size: 'small',
          onClick: () => handleEdit(row)
        }, { default: () => t('page.userManagement.groups.edit') }),
        h(NButton, {
          size: 'small',
          type: row.status === 'active' ? 'warning' : 'success',
          onClick: () => handleToggleStatus(row.id)
        }, { default: () => row.status === 'active' ? t('page.userManagement.groups.disable') : t('page.userManagement.groups.enable') }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDelete(row.id)
        }, {
          default: () => t('page.userManagement.groups.confirmDelete'),
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            disabled: row.userCount > 0
          }, { default: () => t('page.userManagement.groups.delete') })
        })
      ]);
    }
  }
]);

// 事件处理函数
function handleSearch() {
  pagination.page = 1;
}

function handleReset() {
  searchQuery.value = '';
  selectedStatus.value = null;
  pagination.page = 1;
}

function handleAdd() {
  message.info(t('common.lookForward'));
}

function handleViewMembers(row: GroupInfo) {
  message.info(`${t('page.userManagement.groups.viewMembers')} "${row.name}"`);
}

function handleEdit(row: GroupInfo) {
  message.info(`${t('page.userManagement.groups.edit')} "${row.name}"`);
}

function handleToggleStatus(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group) {
    group.status = group.status === 'active' ? 'inactive' : 'active';
    const statusText = group.status === 'active' ? t('page.userManagement.groups.enableSuccess') : t('page.userManagement.groups.disableSuccess');
    message.success(statusText);
  }
}

function handleDelete(id: string) {
  const index = groups.value.findIndex(g => g.id === id);
  if (index > -1) {
    groups.value.splice(index, 1);
    message.success(t('page.userManagement.groups.deleteSuccess'));
  }
}

function handleBatchEnable() {
  message.success(t('page.userManagement.groups.batchEnableSuccess'));
}

function handleBatchDisable() {
  message.success(t('page.userManagement.groups.batchDisableSuccess'));
}

function handleBatchDelete() {
  message.success(t('page.userManagement.groups.batchDeleteSuccess'));
}

function updatePagination() {
  // 分页更新逻辑
}
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="16" class="p-16px">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
            {{ t('page.userManagement.groups.title') }}
          </h1>
          <p class="text-14px text-gray-600 dark:text-gray-400">
            {{ t('page.userManagement.groups.description') }}
          </p>
        </div>
        <div class="flex items-center space-x-12px">
          <NButton type="primary" @click="handleAdd">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            {{ t('page.userManagement.groups.createGroup') }}
          </NButton>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <NCard :bordered="false" class="card-wrapper">
        <NSpace vertical :size="16">
          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NGi span="24 s:24 m:8">
              <NInput
                v-model="searchQuery"
                :placeholder="t('page.userManagement.groups.searchPlaceholder')"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <SvgIcon icon="mdi:magnify" />
                </template>
              </NInput>
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedStatus"
                :placeholder="t('page.userManagement.groups.selectStatus')"
                :options="statusOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:8">
              <NSpace>
                <NButton type="primary" @click="handleSearch">
                  <template #icon>
                    <SvgIcon icon="mdi:magnify" />
                  </template>
                  {{ t('common.search') }}
                </NButton>
                <NButton @click="handleReset">
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" />
                  </template>
                  {{ t('common.reset') }}
                </NButton>
              </NSpace>
            </NGi>
          </NGrid>

          <!-- 批量操作 -->
          <div class="flex items-center justify-between pt-16px border-t border-gray-200 dark:border-gray-700">
            <div class="text-14px text-gray-600 dark:text-gray-400">
              {{ t('page.userManagement.groups.selectedCount', { count: checkedRowKeys.length }) }}
            </div>
            <NSpace>
              <NButton
                size="small"
                type="success"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchEnable"
              >
                {{ t('page.userManagement.groups.batchEnable') }}
              </NButton>
              <NButton
                size="small"
                type="warning"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDisable"
              >
                {{ t('page.userManagement.groups.batchDisable') }}
              </NButton>
              <NButton
                size="small"
                type="error"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDelete"
              >
                {{ t('page.userManagement.groups.batchDelete') }}
              </NButton>
            </NSpace>
          </div>
        </NSpace>
      </NCard>

      <!-- 分组列表表格 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <span class="text-16px font-medium">{{ t('page.userManagement.groups.table.title') }}</span>
        </template>

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="paginatedGroups"
          :row-key="row => row.id"
          :scroll-x="1400"
          flex-height
          style="min-height: 400px"
          class="responsive-table"
        />

        <div class="flex items-center justify-between mt-16px">
          <div class="text-14px text-gray-600">
            {{ t('page.userManagement.groups.showingRecords', {
              start: (pagination.page - 1) * pagination.pageSize + 1,
              end: Math.min(pagination.page * pagination.pageSize, pagination.total),
              total: pagination.total
            }) }}
          </div>
          <NPagination
            v-model:page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            show-size-picker
            show-quick-jumper
            @update:page="updatePagination"
            @update:page-size="updatePagination"
          />
        </div>
      </NCard>
    </NSpace>
  </div>
</template>

<style scoped>
/* 必要的组件深度样式，UnoCSS无法完全替代的部分 */
</style>
